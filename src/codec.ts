import {
  Codec,
  getU8Codec,
  getU16<PERSON>odec,
  getU32<PERSON>odec,
  getU64<PERSON>odec,
  getArrayCodec,
  getStructCodec,
  getAddressCodec,
  getNullableCodec,
} from "gill";

type ExtractCodecValueType<T> = T extends Codec<any, infer V> ? V : never;

function getPermissionsCodec() {
  return getStructCodec([["mask", getU8Codec()]]);
}

function getMemberCodec() {
  return getStructCodec([
    ["key", getAddressCodec()],
    ["permissions", getPermissionsCodec()],
  ]);
}

export function getMultisigAccountCodec() {
  return getStructCodec([
    ["accountDiscriminator", getArrayCodec(getU8Codec(), { size: 8 })],
    ["createKey", getAddressCodec()],
    ["configAuthority", getAddressCodec()],
    ["threshold", getU16Codec()],
    ["timeLock", getU32Codec()],
    ["transactionIndex", getU64Codec()],
    ["staleTransactionIndex", getU64Codec()],
    ["rentCollector", getNullableCodec(getAddressCodec())],
    ["bump", getU8Codec()],
    ["members", getArrayCodec(getMemberCodec())],
  ]);
}

export type MultisigAccount = ExtractCodecValueType<
  ReturnType<typeof getMultisigAccountCodec>
>;
