import superjson from "superjson";
import { PROGRAM_ID } from "@sqds/multisig";
import { drizzle } from "drizzle-orm/d1";
import { address, createSolanaClient, fetchEncodedAccount } from "gill";

const SQUADS_PROGRAM_ID = address(PROGRAM_ID.toString());
const MULTISIG_ACCOUNT_DISCRIMINATOR_BASE64 = "4HR5ukShT+w=";

import { getMultisigAccountCodec, MultisigAccount } from "./codec";
import { accountsSchema } from "./db/schema";

export default {
  async fetch(request: Request, env: Env) {
    const db = drizzle(env.DB);
    const { rpc } = createSolanaClient({
      urlOrMoniker: env.RPC_URL || "",
    });

    let count = 0;
    let allAccounts = [];
    let paginationKey = null;
    let accounts: MultisigAccount[] = [];

    try {
      do {
        const res = await rpc
          .getProgramAccountsV2(SQUADS_PROGRAM_ID, {
            encoding: "jsonParsed",
            limit: 10000,
            ...(paginationKey ? { paginationKey } : {}),
            filters: [
              {
                memcmp: {
                  offset: 0,
                  encoding: "base64",
                  bytes: MULTISIG_ACCOUNT_DISCRIMINATOR_BASE64,
                },
              },
            ],
          })
          .send();

        if (res.accounts.length) {
          allAccounts.push(...res.accounts);
        }

        paginationKey = res.paginationKey;
      } while (paginationKey);
    } catch (e) {
      console.error("Error to fetch accounts: ", e);
    }

    try {
      accounts = await Promise.all(
        allAccounts.map(async (account) => {
          const accountData = await fetchEncodedAccount(rpc, account.pubkey);
          console.log("pubkey: ", account.pubkey);
          console.log("count: ", count);

          count++;

          return getMultisigAccountCodec().decode(accountData.data);
        })
      );
    } catch (e) {
      console.error("Error to decode accounts: ", e);
    }

    console.log("accounts: ", accounts.length);

    // try {
    //   await Promise.all(
    //     accounts.map(async (multisig) => {
    //       await db.insert(accountsSchema).values({
    //         accountDiscriminator: multisig.accountDiscriminator,
    //         createKey: multisig.createKey,
    //         configAuthority: multisig.configAuthority,
    //         threshold: Number(multisig.threshold),
    //         timeLock: Number(multisig.timeLock),
    //         transactionIndex: Number(multisig.transactionIndex),
    //         staleTransactionIndex: Number(multisig.staleTransactionIndex),
    //         rentCollector: multisig.rentCollector,
    //         bump: Number(multisig.bump),
    //         members: multisig.members.map((member) => ({
    //           key: member.key,
    //           permissions: Number(member.permissions.mask),
    //         })),
    //       });
    //     })
    //   );
    // } catch (e) {
    //   console.error("Error to insert accounts: ", e);
    // }

    return new Response("OK");
  },
};
