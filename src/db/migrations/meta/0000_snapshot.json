{"version": "6", "dialect": "sqlite", "id": "9cbf3487-ac6e-4705-af96-676e55be064c", "prevId": "********-0000-0000-0000-************", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "account_discriminator": {"name": "account_discriminator", "type": "blob", "primaryKey": false, "notNull": true, "autoincrement": false}, "create_key": {"name": "create_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "config_authority": {"name": "config_authority", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "threshold": {"name": "threshold", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_lock": {"name": "time_lock", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "transaction_index": {"name": "transaction_index", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "stale_transaction_index": {"name": "stale_transaction_index", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "rent_collector": {"name": "rent_collector", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "bump": {"name": "bump", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "members": {"name": "members", "type": "blob", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}