import { sqliteTable, text, integer, blob } from "drizzle-orm/sqlite-core";

export const accountsSchema = sqliteTable("accounts", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  accountDiscriminator: blob("account_discriminator", {
    mode: "json",
  }).notNull(), // 8 bytes
  createKey: text("create_key").notNull(),
  configAuthority: text("config_authority").notNull(),
  threshold: integer("threshold").notNull(), // u16
  timeLock: integer("time_lock").notNull(), // u32
  transactionIndex: integer("transaction_index").notNull(), // u64
  staleTransactionIndex: integer("stale_transaction_index").notNull(), // u64
  rentCollector: text("rent_collector"), // nullable address
  bump: integer("bump").notNull(), // u8
  members: blob("members", { mode: "json" }).notNull(),
});
