{"name": "multisig-accounts", "module": "./src/index.ts", "type": "module", "scripts": {"dev": "wrangler dev --local", "types": "wrangler types", "migrate": "drizzle-kit migrate", "generate": "drizzle-kit generate", "migrate:local": "wrangler d1 migrations apply multisig-accounts --local", "studio": "drizzle-kit studio"}, "devDependencies": {"wrangler": "^4.38.0", "@types/bun": "latest", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.5", "drizzle-zod": "^0.8.3"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"gill": "^0.11.0", "superjson": "^2.2.2", "@sqds/multisig": "^2.1.4"}}